********************************************************************************
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
********************************************************************************
************************************************* 
* WARNING: DO NOT EDIT THIS FILE                * 
* WARNING: THIS FILE IS AUTOMATICALLY GENERATED * 
************************************************* 
* 
********************************
* Serial Channel 00 
********************************
* C sio->qstype[0] 1 
* C sio->qscschn[0] 704 
* C sio->qsbaud[0] 9600 
* C sio->qsdbit[0] 8 
* C sio->qsparity[0] 0
* C sio->qssbit[0] 1 
* C sio->qscmode[0] 0
g sio->qsrxbuf00 sio->qsrxbofs[0] sio->sercfg 
g sio->qstxbuf00 sio->qstxbofs[0] sio->sercfg 
********************************
* Serial Channel 01 
********************************
* C sio->qstype[1] 1 
* C sio->qscschn[1] 705 
* C sio->qsbaud[1] 19200 
* C sio->qsdbit[1] 8 
* C sio->qsparity[1] 0
* C sio->qssbit[1] 1 
* C sio->qscmode[1] 0
g sio->qsrxbuf01 sio->qsrxbofs[1] sio->sercfg 
g sio->qstxbuf01 sio->qstxbofs[1] sio->sercfg 
********************************
* Serial Channel 02 
********************************
* C sio->qstype[2] 1 
* C sio->qscschn[2] 706 
* C sio->qsbaud[2] 19200 
* C sio->qsdbit[2] 8 
* C sio->qsparity[2] 0
* C sio->qssbit[2] 1 
* C sio->qscmode[2] 0
g sio->qsrxbuf02 sio->qsrxbofs[2] sio->sercfg 
g sio->qstxbuf02 sio->qstxbofs[2] sio->sercfg 
********************************
* Serial Channel 03 
********************************
* C sio->qstype[3] 1 
* C sio->qscschn[3] 707 
* C sio->qsbaud[3] 19200 
* C sio->qsdbit[3] 8 
* C sio->qsparity[3] 0
* C sio->qssbit[3] 1 
* C sio->qscmode[3] 0
g sio->qsrxbuf03 sio->qsrxbofs[3] sio->sercfg 
g sio->qstxbuf03 sio->qstxbofs[3] sio->sercfg 
********************************
* Serial Channel 04 
********************************
* C sio->qstype[4] 1 
* C sio->qscschn[4] 708 
* C sio->qsbaud[4] 9600 
* C sio->qsdbit[4] 8 
* C sio->qsparity[4] 0
* C sio->qssbit[4] 1 
g sio->qsrxbuf04 sio->qsrxbofs[4] sio->sercfg 
g sio->qstxbuf04 sio->qstxbofs[4] sio->sercfg 
********************************
* Serial Channel 05 
********************************
* C sio->qstype[5] 1 
* C sio->qscschn[5] 709 
* C sio->qsbaud[5] 9600 
* C sio->qsdbit[5] 8 
* C sio->qsparity[5] 0
* C sio->qssbit[5] 1 
g sio->qsrxbuf05 sio->qsrxbofs[5] sio->sercfg 
g sio->qstxbuf05 sio->qstxbofs[5] sio->sercfg 
*
*** End of Serial Configuration ***
