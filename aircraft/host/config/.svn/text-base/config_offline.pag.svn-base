********************************************************************************
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
********************************************************************************
log load_offline
color off
ac off
case lower
command off
#data reset
c elec->busoveride 1
c elec->cboveride 1
*
p '../config/simnum.pag'
*
p '../config/b350_table.pag'
*
# needed for offline rudder boost arming
c pl21->lioc.bus3.ioc_valid   1
c pl21->rioc.bus3.ioc_valid   1
#
c cldg->ucldon 1    # turn on controls
c rtctl.lczonline 1 # turn controls on (offline only)
c rtctl.lchostovrd 0 # Host override off
c rca.on(1:6) 1 # turn on controls
*
*Disable wxr_terrain from running offline
c sim->quitflag[400] 1
*Disable all serial read modules
c sim->quitflag[39] 1
c sim->quitflag[130] 1
c sim->quitflag[131] 1
c sim->quitflag[177] 1
log off
