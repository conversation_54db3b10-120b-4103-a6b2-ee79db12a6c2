********************************************************************************
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
********************************************************************************
* Start of EGPWS Tables
p '../lfi_egpws/egpws_bank_angle.nsa'
p '../lfi_egpws/egpws_glideslope.nsa'
p '../lfi_egpws/egpws_glideslope_hard.nsa'
p '../lfi_egpws/egpws_mode1.nsa'
p '../lfi_egpws/egpws_mode1_pull_up.nsa'
#p '../lfi_egpws/egpws_mode2_a.nsa'
p '../lfi_egpws/egpws_mode2_a_pull_up.nsa'
p '../lfi_egpws/egpws_mode2_a_terrain.nsa'
#p '../lfi_egpws/egpws_mode2_b.nsa'
p '../lfi_egpws/egpws_mode2_b_pull_up.nsa'
p '../lfi_egpws/egpws_mode2_b_terrain.nsa'
p '../lfi_egpws/egpws_mode3.nsa'
p '../lfi_egpws/egpws_mode4_a.nsa'
p '../lfi_egpws/egpws_mode4_b.nsa'
p '../lfi_egpws/egpws_mode4_c.nsa'
p '../lfi_egpws/egpws_mode4_flaps.nsa'
p '../lfi_egpws/egpws_mode4_gear.nsa'
#p '../lfi_egpws/egpws_pull_up.nsa'
#p '../lfi_egpws/egpws_pull_up_desensitized.nsa'
#p '../lfi_egpws/egpws_sink_rate.nsa'
#p '../lfi_egpws/egpws_sink_rate_desensitized.nsa'
p '../lfi_egpws/egpws_tcf.nsa'
* End of EGPWS Tables
