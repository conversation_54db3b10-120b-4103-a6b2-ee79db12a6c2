################################################################################
# %OPLICENSE%                                                                  *
#             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
#                                                                              *
# This software and the data incorporated herein is licensed, not sold,        *
# and is protected by Federal and State copyright and related intellectual     *
# property laws. It may not be disclosed, copied, reversed engineered or used  *
# in any manner except in strict accordance with the terms and conditions of   *
# a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
# the one King Air B350/200 simulator to which it relates. Sale, lease or      *
# other transfer of the simulator does not authorize the transferee to use     *
# this software and the data incorporated herein unless strict compliance with *
# the terms of the License referenced above has been met. A copy of the        *
# License is available from OPINICUS upon request. Third party licensed        *
# proprietary data may also be incorporated herein and is subject to the       *
# terms of those licenses.                                                     *
# %OPLICENSE%                                                                  # 
################################################################################
LDIR = af
include ../exec/Makefile.rules

#CFLAGS += -D__REV__=$(DREV)
CFLAGS += -D__REV__=-1

BASE = lib$(ACDES)$(LDIR).a

LIB = $(BASE)$(LINUXVERSION)

all : $(LIB)

OBJS = af_main.o  \
       af_io.o    \
       af_shm.o   

OBJ_SO = $(OBJS:%.o=%.so)

LIB_OBJS = $(OBJS:%.o=$(LIB)(%.o))

$(LIB) : $(LIB_OBJS)
	ln -fs $(LIB) $(BASE)

DEPEND = .depend 

clean : 
	rm -f $(LIB) *.o *.so $(DEPEND)

.INTERMEDIATE : $(OBJS) $(OBJ_SO)

op :
	make -f makefile_op

clean_op :
	make -f makefile_op clean

# Automatic dependency rules
$(DEPEND): $(wildcard $(patsubst %,%/*.c,$(subst :, ,$(VPATH)))) makefile
	$(CC) $(CFLAGS) -MM -MG $(filter-out makefile, $^)  |    \
	/usr/bin/gawk '{sub(/:/,": \044{FRC}");print}' > $@

include $(DEPEND)
