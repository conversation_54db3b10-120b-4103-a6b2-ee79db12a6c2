################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Characteristic Motion Vibrations - Stall Buffet
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    3.f.5
#  --------
#
#  TEST NUMBER: f03605100000000
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               3.f.5
# Maneuver:          Characteristic Motion Vibrations - Stall Buffet
# File No:           f03605100000000.st
# Flight Test Date:  11/29/12
# Start FT_Time:     0.02
# Stop  FT_Time:     155.00
# Start OP_Time:     40.00
# Stop  OP_Time:     90.00
# -------------------------------------
# Flap:              14.00
# Gear:              0.00
# Weight:            12258.7
# Hp:                8437.8
# Kcas:              100.43
# Mach:              0.1770
# TAT:               -19.899
#
WAIT 1
C atst->kdatstno 3605100000000
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Characteristic Motion Vibrations - Stall Buffet'
strcpy atst->faa_tstno '3.f.5.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'f03605100000000.st'
#
atst->ft_time_hack[0]=40.0
atst->ft_time_hack[1]=90.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f03605100000000.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
#PLOT BEGIN
#PLOT POINTS
#PLOT TITLE 'Characteristic Motion Vibrations - Stall Buffet'
#PLOT FILE 'f03605100000000.PLT'
#P '$AUTO/plot/f03605100000000.plt'
#PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f03605100000000.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
flt->rfxpitcm=    0.00   # theta (deg)
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 648.4  # Left Total  = 648.1 (Main=648.1,Aux=  0.0)
fuel->fdemtnk[1]= 648.4  # Right Total = 648.6 (Main=648.6,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     12258.7  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 12058.7 12458.7
flt->dmd_cg_in=194.836     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     20081.7  # Ixx (slug-ft^2)
flt->fdemiyy=     18048.7  # Iyy (slug-ft^2)
flt->fdemizz=     34785.0  # Izz (slug-ft^2)
flt->fdemixz=      1862.8  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -21.32  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt= 8437.8    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp  8387.8  8487.8
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp= 8437.8   # demanded alt (ft)
#
flt->rtcvc=     93.5   # vc (kts)
flt->rzcroc=      0.0    # -907.26   # roc (fpm)
flt->rfxpitcm=    4.09   # theta (deg)
flt->rfxrolcm=    0.00   # phi (deg)
flt->rfxhdgcm=  59.53+   0.00   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   0.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   0.00  # gear
wait 20 ctl->gear.avg_pos -0.10  0.10
wait 20 ctl->flap.avg_surf_pos -0.50 0.50 # (deg)
#
flt->fdemstab=    6.924   #   6.924 longitudinal trim pos.
atst->fdemrtab=   0.000   #  -1.715 rudder trim pos.
atst->fdematab=   0.000   #   2.415 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      65.613  # Engine 1 N1 (perc)
flt->rzcn1[1]=      63.347  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1434.748  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1414.505  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   17.28   #    16.15 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   17.28   #    18.42 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     20.08   #    19.85 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.08   #    20.31 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    33.00   #    32.96 Engine 1 TLA (deg)
eng->fdemeng[1]=    33.00   #    33.15 Engine 2 TLA (deg)
atst->rzctq[0]=   -100.00   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=   -100.00   # Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.54  # Cmd Brake Pos
atst->fsettoeb=F
atst->fsettoer=F
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.44  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=F     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)    -1.146
flt->rzcvdot=   0.000   # vdot (ft/sec**2)     0.368
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.829
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.119
flt->rzcqds =   0.000   # q (deg/sec)          0.248
flt->rzcrds =   0.000   # r (deg/sec)          0.110
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=T
atst->lfxailt=T
atst->lfxrudt=T
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=T     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime= -5.00
flt->rdatend= 23.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   4.09   # theta (deg)
flt->rfxrolcm=   0.00   # phi (deg)
flt->rfxhdgcm=  59.53+   0.00   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=194.836     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
mx->vib_ena=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
flt->lztqhld=F
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F

#Power Spectral Analysis
if (sim->online){
wait 5  
sys setupacc 18 2048 0
sys get_osnap f03605100000000
}
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
WAIT 2
cd '../plt/vibes'
sys make
WAIT 2
sys cp  'f03605100000000.TXT' ../
sys cp 'f03605100000000.TXT.gz' ../
sys pdf f03605100000000
#
x
