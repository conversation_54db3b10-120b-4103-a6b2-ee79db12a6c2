
################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Flight Idle or Equivalent
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    5.b.4
#  --------
#
#  TEST NUMBER: f05204000000000
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               5.b.4
# Maneuver:          Flight Idle or Equivalent
WAIT 1
C atst->kdatstno 5204000000000
#
strcpy atst->tst_title 'Flight Idle or Equivalent'
strcpy atst->faa_tstno '5.b.4.a'
#
WAIT 1
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 900.0  # Left Total 
fuel->fdemtnk[1]= 900.0  # Right Total 
fuel->fsetfuel=T
flt->fdemgw=     13950.0  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 13900.0 14000.0
flt->dmd_cg_in=196.310     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     16393.6  # Ixx (slug-ft^2)
flt->fdemiyy=     17747.7  # Iyy (slug-ft^2)
flt->fdemizz=     30813.9  # Izz (slug-ft^2)
flt->fdemixz=      1818.5  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= 16.1  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
c flt.fhggnd 40000
c flt.fhp 40000
# flt->fdemhter=0.0      # 0.0 field elevation (ft)
# flt->fsethter=T
# flt->fdemalt=  551.1    # alt (ft)
# flt->fsetalt=1
# wait 20 flt->fhp   501.1   601.1
flt->fdemhter=640.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fhter = 640.0
# atst->rzcalthp=  640.1   # demanded alt (ft)
flt->fmtor=T
flt->fipfld=T
WAIT 2
atst->ftdlat=     0.00   # lat
atst->ftdlon=     0.00   # long
atst->ftdhdg=    72.06 +   0.00   # mag hdg + mag var
flt->fdist=       0.0   # takeoff distance
flt->fgyecg=      0.0   # east CG position from reference point
flt->fgxecg=      0.0   # north CG position from reference point
#
atst->rtcvg=      0.00   #      0.00   # ve (kts)
# flt->rzcroc=     15.35   # roc (fpm)
# flt->rfxpitcm=    1.36   # theta (deg)
# flt->rfxrolcm=    0.00   #   0.97   # phi (deg)
# flt->rfxhdgcm=  72.06+   0.00   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   1.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 13.50 14.50 # (deg)
#
flt->fdemstab=   -2.991   #  -2.991 longitudinal trim pos.
atst->fdemrtab=   0.000   #  -0.495 rudder trim pos.
atst->fdematab=   0.000   #  13.287 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
#flt->rzcn1[0]=    0.0 # Engine 1 N1 (perc)
#flt->rzcn1[1]=    0.0 # Engine 2 N1 (perc)
#atst->rzcn2[0]=   0.0  # Engine 1 N2 (perc)
#atst->rzcn2[1]=   0.0  # Engine 2 N2 (perc)
if(eng->ng[0]<50.0) {
   c eng->ebstart[0] 1
}
if(eng->ng[1]<50.0) {
   c eng->ebstart[1] 1
}
eng->dmd_prla[0]=  45.0    # Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=  45.0    # Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=   20.0 # Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=   20.0 # Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    33.0   # Engine 1 TLA (deg)
eng->fdemeng[1]=    33.0   # Engine 2 TLA (deg)
#atst->rzctq[0]=    0.0 # Engine 1 Torque (ft-lb)
#atst->rzctq[1]=    0.0 # Engine 2 Torque (ft-lb)
wait 1
wait 20 eng->ng[1] 55.0  70.0
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=  20.0 #0.07  # Cmd Brake Pos
atst->fdemtoer=  20.0 #0.00  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]= 143.30  # Cmd Brk Press
atst->rzcbp[1]= 205.86  # Cmd Brk Press
#
sio->LXDI010710 = T # Battery ON
sio->LXDI010711 = T # Battery Bus ON
sio->LXDI010702 = T # Avionics ON 
sio->LXDI010703 = T # External Power Switch OFF
elec->ios_ext_pwr = F # External Power Disconnected
#
##############################################
#
#  TRIMMER
#  -------
#
wait 3
atst->kaxpmode=3        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=3        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=3        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=0          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)    -0.129
flt->rzcvdot=   0.000   # vdot (ft/sec**2)    -0.046
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.125
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.090
flt->rzcqds =   0.000   # q (deg/sec)          0.012
flt->rzcrds =   0.000   # r (deg/sec)         -0.058
atst->fdemnw = -2.664   # Dem NW Angle   -2.664
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
atst->rcxlonsk=-0.***********/(6.3)
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=F        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=F        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend=30.0
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
# flt->rfxpitcm=   1.36   # theta (deg)
# flt->rfxrolcm=   0.97   # phi (deg)
# flt->rfxhdgcm=  72.06+   0.00   # hdg (deg)
#
# Generic Runway Setup
vis->atg_sel_hdg=atst->ftdhdg
vis->atg_sel_elev=flt->fdemhter
P '$AUTO/atg/ftakeoff.pag'
#
WAIT 1
#
flt->dmd_cg_in=196.310     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
