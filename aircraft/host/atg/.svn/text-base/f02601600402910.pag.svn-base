################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Ground Effect, 50 ft
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.f.1
#  --------
#
#  TEST NUMBER: f02601600402910
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.f.1
# Maneuver:          Ground Effect, 50 ft
# File No:           k1501004_cmb.csv
# Flight Test Date:  01/12/01
# Start FT_Time:     0.02
# Stop  FT_Time:     90.00
# Start OP_Time:     30.00
# Stop  OP_Time:     55.00
# -------------------------------------
# Flap:              35.00
# Gear:              1.00
# Weight:            12628.5
# Hp:                441.1
# Kcas:              104.72
# Mach:              0.1581
# TAT:               -17.081
#
WAIT 1
C atst->kdatstno 2601600402910
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Ground Effect, 50 ft'
strcpy atst->faa_tstno '2.f.1.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k1501004_cmb.csv'
#
atst->ft_time_hack[0]=30.0
atst->ft_time_hack[1]=55.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02601600402910.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Ground Effect, 50 ft'
PLOT FILE 'f02601600402910.PLT'
P '$AUTO/plot/f02601600402910.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02601600402910.lfi'
#
##############################################
# Reposition to Home Airport Location
P '$AUTO/atg/init_takeoff_repos.pag'
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 1158.2  # Left Total  = 1169.8 (Main=1169.8,Aux=  0.0)
fuel->fdemtnk[1]= 1158.2  # Right Total = 1146.7 (Main=1146.7,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     12628.5  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 12428.5 12828.5
flt->dmd_cg_in=197.851     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     28607.1  # Ixx (slug-ft^2)
flt->fdemiyy=     17934.5  # Iyy (slug-ft^2)
flt->fdemizz=     43192.9  # Izz (slug-ft^2)
flt->fdemixz=      1849.5  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -18.20  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   318.25+flt->magvar #0.00  #    0.00 # Wind Direction
flt->fdemwsp=   0.00  # 12.466-5.0 #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemalt=  1000.0    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   950.0  1050.0
flt->fdemhter=384.0-1.73       # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=  444.1-0.5    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   391.1   491.1
atst->rzcalthp=  444.1-0.5   # demanded alt (ft)
#
flt->rtcvc=    104.72   # vc (kts)
flt->rzcroc=  60.0+20.0 #18.0+60.0+45.0+20.0-50.0+33.0   # roc (fpm)
flt->rfxpitcm=    3.12   # theta (deg)
flt->rfxrolcm=    1.20   # phi (deg)
flt->rfxhdgcm= 319.59+flt->magvar   # hdg (deg)
atst->ftdhdg=  319.59+   0.00       # mag hdg + mag var
#
# Generic Runway Setup
vis->atg_sel_hdg=atst->ftdhdg-3.0
vis->atg_sel_elev=flt->fdemhter
P '$AUTO/atg/ftakeoff.pag'
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   2.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 34.50 35.50 # (deg)
#
flt->fdemstab=   10.034   #  10.034 longitudinal trim pos.
atst->fdemrtab=  -1.613   #  -1.613 rudder trim pos.
atst->fdematab=   1.282   #   1.282 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      81.807  # Engine 1 N1 (perc)
flt->rzcn1[1]=      81.935  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1522.906  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1523.063  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   19.31   #    18.97 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   19.31   #    19.65 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     20.04   #    19.76 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.04   #    20.32 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    46.69   #    45.72 Engine 1 TLA (deg)
eng->fdemeng[1]=    46.69   #    47.66 Engine 2 TLA (deg)
atst->rzctq[0]=   1050.03   #  1067.06 Engine 1 Torque (ft-lb)
atst->rzctq[1]=   1050.03   #  1033.00 Engine 2 Torque (ft-lb)
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.32  # Cmd Brake Pos
atst->fsettoeb=F
atst->fsettoer=F
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   3.43  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
atst->rzdgain[8]=0.5
#atst->rzpitaltk=0.2
#atst->rzcpitaltlim=10.0
#
flt->rzcudot=   0.142   # udot (ft/sec**2)     0.142
flt->rzcvdot=   0.000   # vdot (ft/sec**2)     0.259
flt->rzcwdot=   0.0 #1.100   # wdot (ft/sec**2)   -33.923
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -1.662
flt->rzcqds =  -0.500   # q (deg/sec)          0.558
flt->rzcrds =   0.000   # r (deg/sec)         -0.041
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=T     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=T       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=T       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
atst->rzcaltrad = 49.22499
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 10.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   3.12   # theta (deg)
flt->rfxrolcm=   1.20   # phi (deg)
flt->rfxhdgcm= 319.59+flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=197.851     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
atst->rdatrmlm(6)=0.2
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
