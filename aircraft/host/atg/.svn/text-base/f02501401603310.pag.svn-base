################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Normal Landing - Brakes
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.e.1
#  --------
#
#  TEST NUMBER: f02501401603310
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.e.1
# Maneuver:          Normal Landing - Brakes
# File No:           k1701016_cmb.csv
# Flight Test Date:  01/13/01
# Start FT_Time:     0.00
# Stop  FT_Time:     97.98
# Start OP_Time:     45.00
# Stop  OP_Time:     80.00
# -------------------------------------
# Flap:              14.00
# Gear:              1.00
# Weight:            12316.2
# Hp:                529.9
# Kcas:              115.45
# Mach:              0.1747
# TAT:               -16.567
#
WAIT 1
C atst->kdatstno 2501401603310
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Normal Landing, Medium Weight'
strcpy atst->faa_tstno '2.e.1.b'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k1701016_cmb.csv'
#
atst->ft_time_hack[0]=45.0
atst->ft_time_hack[1]=80.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02501401603310.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Normal Landing, Medium Weight'
PLOT FILE 'f02501401603310.PLT'
P '$AUTO/plot/f02501401603310.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02501401603310.lfi'
#
##############################################
# Reposition to Home Airport Location
P '$AUTO/atg/init_takeoff_repos.pag'
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 1002.1  # Left Total  = 998.6 (Main=998.6,Aux=  0.0)
fuel->fdemtnk[1]= 1002.1  # Right Total = 1005.6 (Main=1005.6,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     12316.2  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 12116.2 12516.2
flt->dmd_cg_in=197.711     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     25209.6  # Ixx (slug-ft^2)
flt->fdemiyy=     17925.0  # Iyy (slug-ft^2)
flt->fdemizz=     39788.3  # Izz (slug-ft^2)
flt->fdemixz=      1847.6  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -17.97  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=319.888916+14.2 #flt->magvar #   235.39+  flt->magvar  #    0.00 # Wind Direction
flt->fdemwsp=1.7 #0.0 #7.1977158-5.5 #    0.51  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=  529.9    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   479.9   579.9
flt->fdemhter=272.0 #272.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp=  529.9   # demanded alt (ft)
#
# Generic Runway Setup
vis->atg_sel_hdg=317.0
vis->atg_sel_lat_tdz=27.9741157611
vis->atg_sel_lon_tdz=-82.5089946055
P '$AUTO/atg/flanding.pag'
#
#flt->rtcvc=   115.45 #115.9 #115.45+1.0 # 117.0 #  # vc (kts)
atst->rtcvg=108.26
flt->rzcroc=   -674.41 +25.0+30.0 + 60.0-25.0   # roc (fpm)
#flt->rzcroc=   -731.0  # roc (fpm)
flt->rfxpitcm=    1.83   # theta (deg)
flt->rfxrolcm=   -1.04   # phi (deg)
flt->rfxhdgcm= 319.89+  flt->magvar   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   1.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 13.50 14.50 # (deg)
#
flt->fdemstab=  5.8 #  6.491   #   6.491 longitudinal trim pos.
atst->fdemrtab=   0.622   #   0.622 rudder trim pos.
atst->fdematab=   0.419   #   0.419 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      76.562  # Engine 1 N1 (perc)
flt->rzcn1[1]=      77.524  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1567.987  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1576.103  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   31.86   #    31.31 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   31.86   #    32.41 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.96   #    19.63 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     19.96   #    20.29 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    43.24   #    42.10 Engine 1 TLA (deg)
eng->fdemeng[1]=    43.24   #    44.39 Engine 2 TLA (deg)
atst->tla_offset[0]=-0.3
atst->tla_offset[1]=0.0 
atst->rfxcpla = 42.54
atst->rfxcpla = 42.94
atst->rzctq[0]=    340.63   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=    403.73   # Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.37  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   1.44  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
atst->rzdgain[8]=0.5
atst->rzpitaltk=0.2
atst->rzcpitaltlim=10.0
atst->rztqdgain[:]=0.8 #0.3
atst->rzpgain[0]=0.15
# atst->rztqpgain[:]=0.0
# atst->rztqidgain[:]=8000.0
#
#flt->rzcudot=  -0.440   # udot (ft/sec**2)    -0.441
flt->rzcudot=  -0.200-0.25+0.04-0.07   # udot (ft/sec**2)    -0.441
flt->rzcvdot=   0.000   # vdot (ft/sec**2)    -0.924
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.304
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.489
flt->rzcqds =   0.000   # q (deg/sec)          0.095
flt->rzcrds =   0.000   # r (deg/sec)          0.430
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=T     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=T       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=T       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=T        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=T         #  FLARE MODE
atst->rzflareh=20.0 #46.0
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
# flt->rdatend= 35.00
flt->rdatend=32.0
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   1.83   # theta (deg)
flt->rfxrolcm=  -1.04   # phi (deg)
flt->rfxhdgcm= 319.89+   flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=197.711     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
atst->rztqpgain[:] = 0.015
atst->rztqpgain[:] = 0.030
# Engine Anti-Ice OFF
sio->LXDI010705=T
sio->LXDI010704=T
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
