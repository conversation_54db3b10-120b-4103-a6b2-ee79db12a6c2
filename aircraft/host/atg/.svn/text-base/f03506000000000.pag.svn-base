################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Motion Cueing Performance - Landing Flare
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    3.e.6
#  --------
#
#  TEST NUMBER: f03506000000000 (based on f02501600708610)
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               3.e.6
# Maneuver:          Normal Landing
# File No:           k4001007_cmb.csv
# Flight Test Date:  01/29/01
# Start FT_Time:     0.00
# Stop  FT_Time:     99.98
# Start OP_Time:     12.00
# Stop  OP_Time:     47.00
# -------------------------------------
# Flap:              35.00
# Gear:              1.00
# Weight:            14531.3
# Hp:                657.0
# Kcas:              114.43
# Mach:              0.1735
# TAT:               -11.691
#
WAIT 1
C atst->kdatstno 3506000000000
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Motion Cueing - Landing Flare'
strcpy atst->faa_tstno '3.e.6.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k4001007_cmb.csv'
#
atst->ft_time_hack[0]=12.0
atst->ft_time_hack[1]=47.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f03506000000000.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Motion Cueing - Landing Flare'
PLOT FILE 'f03506000000000.PLT'
P '$AUTO/plot/f03506000000000.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f03506000000000.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 1559.6  # Left Total  = 1532.9 (Main=1214.5,Aux=318.4)
fuel->fdemtnk[1]= 1559.6  # Right Total = 1586.3 (Main=1267.9,Aux=318.4)
fuel->fsetfuel=T
flt->fdemgw=     14531.3  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 14331.3 14731.3
flt->dmd_cg_in=202.987     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     31362.8  # Ixx (slug-ft^2)
flt->fdemiyy=     20897.0  # Iyy (slug-ft^2)
flt->fdemizz=     48855.5  # Izz (slug-ft^2)
flt->fdemixz=      1798.1  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -13.11  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=252.93 + 14.2 #flt->magvar #  184.3 + flt->magvar  #    0.00 # Wind Direction
flt->fdemwsp=6.417 #0.817+4.6 #   6.5   #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=  657.0    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   607.0   707.0
flt->fdemhter=415.0 #417.0-1.0 #-82.9 #0.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp=  657.0   # demanded alt (ft)
#
# Generic Runway Setup
vis->atg_sel_hdg=65.1+3.0
vis->atg_sel_lat_tdz=27.9683 #27.9681086164
vis->atg_sel_lon_tdz=-82.4821 #-82.4832005955
P '$AUTO/atg/flanding.pag'
#
#flt->rtcvc=    114.43   # vc (kts)
atst->rtcvg=115.26
flt->rzcroc=   -716.57+380.0+60.0 # +370.0+40.0   # roc (fpm)
#flt->rzcroc=   -716.57+370+40+60  # roc (fpm)
flt->rfxpitcm=    1.01   # theta (deg)
flt->rfxrolcm=    0.23+0.2   # phi (deg)
flt->rfxhdgcm=  72.93 + flt->magvar   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   2.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 34.50 35.50 # (deg)
#
flt->fdemstab= 3.9 #  4.81 # 5.284   #   5.284 longitudinal trim pos.
atst->fdemrtab=  -1.829   #  -1.829 rudder trim pos.
atst->fdematab=   1.183   #   1.183 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      78.122  # Engine 1 N1 (perc)
flt->rzcn1[1]=      79.111  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1572.495  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1589.983  # Engine 2 N2 (perc)
eng->dmd_prla[0]=  44.34   #25.0 #    43.76 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=  44.34   #25.0 #    44.92 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.99   #    19.70 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     19.99   #    20.28 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    45.51   #    44.50 Engine 1 TLA (deg)
eng->fdemeng[1]=    45.51   #    46.51 Engine 2 TLA (deg)
atst->rfxcpla = 45.7
atst->rzctq[0]=    419.02   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=    467.34   # Engine 2 Torque (ft-lb)
atst->tla_offset[0]=-0.1
atst->tla_offset[1]=0.0
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.07  # Cmd Brake Pos
atst->fdemtoer=   0.11  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=40.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
#flt->ifxtrim=2          # TRIM TYPE
atst->rzdgain[8]=1.5
atst->rzpgain[0]=0.25 #0.15
#atst->rzpitaltk=0.2
#atst->rzcpitaltlim=20.0
atst->rztqdgain[:]=0.8
#
flt->rzcudot=-1.800+0.48-0.2-0.35+0.11   # -1.375 #  udot (ft/sec**2)    -1.471
#flt->rzcudot= -1.471  # -1.375 #  udot (ft/sec**2)    -1.471
#flt->rzcudot= -2.700  # -1.375 #  udot (ft/sec**2)    -1.471
flt->rzcvdot=   0.000   # vdot (ft/sec**2)    -0.302
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -33.002
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.636
flt->rzcqds =   0.000   # q (deg/sec)          0.547
flt->rzcrds =   0.000   # r (deg/sec)         -0.411
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=T     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=T       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=T       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=T        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
#flt->lztqhld=T          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=T         #  FLARE MODE
atst->rzflareh=15.0
#
atst->rzcaltrad=287.8
#
# atst->lzpitcf=T
# atst->rzigcd=-1.5
# atst->rzpgcd=2.5
# atst->lziascf=T
# atst->lzaoacf=T
# atst->rzpgcl=1.0
# atst->rzdgcl=-0.5
# atst->rzigcl=-2.0
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 35.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   1.01   # theta (deg)
flt->rfxrolcm=   0.23+0.2   # phi (deg)
flt->rfxhdgcm=  72.93 + flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=202.987     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
atst->rztqpgain[:] = 0.030
# Engine Anti-Ice OFF
sio->LXDI010705=T
sio->LXDI010704=T
flt->ldatston=T
flt->frz=F
mx->vib_ena = 0         # turn off vibes
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
