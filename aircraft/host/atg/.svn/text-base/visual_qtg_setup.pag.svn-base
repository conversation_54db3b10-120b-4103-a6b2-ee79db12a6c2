
################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
# Visual QTG Test Setup
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
#WAIT LZREADFILE F
WAIT sim->config_inprogress F
flt->frz=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
LFI RESET
#
vis->atg_chg_sound_flag=1
vis->atg_sound_lvl = comm->from_ios.sound_level
comm->from_ios.sound_level=0
#
flt->fhggnd= 10000.0    # alt (ft)
flt->fdemalt=10000.0    # alt (ft)
#FSETHP=T
flt->fsetalt=1
wait 20 flt->fhp 9950.0 10050.0
#FSETHP=F
flt->fsetalt=0
flt->fdemhter=0.0       # field elevation (ft)
flt->fsethter=T
flt->fmtor=T
flt->fipfld=T
flt->nposfrz=T
WAIT 5
atst->rtcvg=0.0
flt->rzcroc=0.00
#
#  ENGINES
#  -------
#
eng->dmd_prla[0]=  11.0   # Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=  11.0   # Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=   20.0    # Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=   20.0    # Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=  33.00  # Engine 1 TLA (deg)
eng->fdemeng[1]=  33.00  # Engine 2 TLA (deg)
wait 1
#
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=  20.0  # Cmd Brake Pos
atst->fdemtoer=  20.0  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
#
#
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T
atst->ldatrmon(2:15)=F
flt->ifxtrim=0
#
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=    0.00
flt->rdatend= 1000.00
atst->ldahisty=T
#
WAIT 1
flt->lfxfastm=T
WAIT 1
#
WAIT 1
#
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=T
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
