################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  *
################################################################################
#
#  TITLE:       Rate of Turn vs NW Steering
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B3501
#  --------
#
#  CRITERIA:    1.a.2
#  --------
#
#  TEST NUMBER: f01102200610110
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               1.a.2
# Maneuver:          Rate of Turn vs NW Steering
# File No:           k4601006_cmb.csv
# Flight Test Date:  02/03/01
# Start FT_Time:     0.00
# Stop  FT_Time:     73.98
# Start OP_Time:     6.00
# Stop  OP_Time:     35.00
# -------------------------------------
# Flap:              0.00
# Gear:              1.00
# Weight:            11553.6
# Hp:                115.8
# Kcas:              0.00
# Mach:              0.0001
# TAT:               -13.308
#
WAIT 1
C atst->kdatstno 1102200610110
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Rate of Turn vs NW Steering, 8 kts'
strcpy atst->faa_tstno '1.a.2.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k4601006_cmb.csv'
#
atst->ft_time_hack[0]=6.0
atst->ft_time_hack[1]=35.0
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f01102200610110.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Rate of Turn vs NW Steering, 8 kts'
PLOT FILE 'f01102200610110.PLT'
P '$AUTO/plot/f01102200610110.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f01102200610110.lfi'
#
##############################################
# Reposition to Home Airport Location
P '$AUTO/atg/init_takeoff_repos.pag'
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 708.3  # Left Total  = 676.9 (Main=676.9,Aux=  0.0)
fuel->fdemtnk[1]= 708.3  # Right Total = 739.7 (Main=739.7,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     11553.6  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 11353.6 11753.6
flt->dmd_cg_in=196.696     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     20731.4  # Ixx (slug-ft^2)
flt->fdemiyy=     17769.6  # Iyy (slug-ft^2)
flt->fdemizz=     35178.9  # Izz (slug-ft^2)
flt->fdemixz=      1813.3  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -13.16  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemalt=  1000.0    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   950.0  1050.0
flt->fsetalt=0
flt->fdemhter=98.0    # 0.0 field elevation (ft)
flt->fsethter=T
flt->fmtor=T
flt->fipfld=T
WAIT 2
atst->ftdlat=     0.00   # lat
atst->ftdlon=     0.00   # long
atst->ftdhdg=   139.97 +   flt->magvar   # mag hdg + mag var
flt->fdist=       0.0   # takeoff distance
flt->fgyecg=      0.0   # east CG position from reference point
flt->fgxecg=      0.0   # north CG position from reference point
#
atst->rtcvg=      1.10   #      1.50   # ve (kts)
# flt->rzcroc=    450.87   # roc (fpm)
# flt->rfxpitcm=    1.99   # theta (deg)
# flt->rfxrolcm=    0.00   #   0.82   # phi (deg)
flt->rfxhdgcm= 139.97+   flt->magvar   # hdg (deg)
#
# Generic Runway Setup
vis->atg_sel_hdg=atst->ftdhdg+6.0
vis->atg_sel_elev=flt->fdemhter
vis->atg_side_offset=150.0
P '$AUTO/atg/ftakeoff.pag'
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   0.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos -0.50 0.50 # (deg)
#
flt->fdemstab=    3.752   #   3.752 longitudinal trim pos.
atst->fdemrtab=  -0.404   #  -0.404 rudder trim pos.
atst->fdematab=   0.391   #   0.391 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      62.520  # Engine 1 N1 (perc)
flt->rzcn1[1]=      62.998  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1138.716  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1184.500  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   43.75   #    42.98 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   43.75   #    44.52 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.96   #    19.64 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     19.96   #    20.27 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    33.0    # 40.5    #    20.71 Engine 1 TLA (deg)
eng->fdemeng[1]=    33.0    # 40.5    #    20.56 Engine 2 TLA (deg)
atst->rzctq[0]=     74.10   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=    104.57   # Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.00  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
wait 3
atst->fdemnw =  0.319   # Dem NW Angle    0.319
atst->kaxpmode=0        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=0        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=0        # PRIMARY CONTROLS YAW MODE
atst->kaxnmode=4        # PRIMARY CONTROLS NW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=F     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=0          # TRIM TYPE
#
flt->rzcudot=   0.633   # udot (ft/sec**2)     0.659
flt->rzcvdot=   0.000   # vdot (ft/sec**2)     0.140
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.415
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.146
flt->rzcqds =   0.000   # q (deg/sec)         -0.129
flt->rzcrds =   0.000   # r (deg/sec)         -0.121
atst->fdemnw =  0.319   # Dem NW Angle    0.319
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
atst->rcxlonsk=13.9801693/(-20.5)  #)
atst->kaxnmode=0        # PRIMARY CONTROLS NW MODE
atst->kaxymode=3        # PRIMARY CONTROLS YAW MODE
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=F        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=F        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 29.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
# flt->rfxpitcm=   1.99   # theta (deg)
# flt->rfxrolcm=   0.82   # phi (deg)
flt->rfxhdgcm= 139.97+   flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=196.696     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
