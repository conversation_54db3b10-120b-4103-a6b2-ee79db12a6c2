################################################################################
# %OPLICENSE%                                                                  *
#             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
#                                                                              *
# This software and the data incorporated herein is licensed, not sold,        *
# and is protected by Federal and State copyright and related intellectual     *
# property laws. It may not be disclosed, copied, reversed engineered or used  *
# in any manner except in strict accordance with the terms and conditions of   *
# a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
# the one King Air B350/200 simulator to which it relates. Sale, lease or      *
# other transfer of the simulator does not authorize the transferee to use     *
# this software and the data incorporated herein unless strict compliance with *
# the terms of the License referenced above has been met. A copy of the        *
# License is available from OPINICUS upon request. Third party licensed        *
# proprietary data may also be incorporated herein and is subject to the       *
# terms of those licenses.                                                     *
# %OPLICENSE%                                                                  # 
################################################################################
LDIR = cldg
include ../exec/Makefile.rules

CFLAGS += -D__REV__=$(DREV)

objs =  h2cxfer.o 

LIBCLDG = libcldg.a$(LINUXVERSION)
LIBLFICLDG = liblficldg.a$(LINUXVERSION)

all : $(objs)
	ln -fs $(LIBCLDG)    lib$(ACDES)cldg.a
	ln -fs $(LIBLFICLDG) lib$(ACDES)lficldg.a
	ln -fs $(LIBCLDG)    libcldg.a
	ln -fs $(LIBLFICLDG) liblficldg.a

clean :
	-rm -f *.o *.so .depend

DEPENDS = $(objs:%.o=%.c) xfer_icd.h

# Automatic dependency rules
.depend: $(DEPENDS) makefile
	$(DODEPEND) $(CFLAGS) -MM -MG $(filter-out makefile, $^) | \
	/usr/bin/gawk '{sub(/:/,": \044{FRC}");print}' > .depend

include .depend
