################################################################################
# %OPLICENSE%                                                                  *
#             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
#                                                                              *
# This software and the data incorporated herein is licensed, not sold,        *
# and is protected by Federal and State copyright and related intellectual     *
# property laws. It may not be disclosed, copied, reversed engineered or used  *
# in any manner except in strict accordance with the terms and conditions of   *
# a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
# the one King Air B350/200 simulator to which it relates. Sale, lease or      *
# other transfer of the simulator does not authorize the transferee to use     *
# this software and the data incorporated herein unless strict compliance with *
# the terms of the License referenced above has been met. A copy of the        *
# License is available from OPINICUS upon request. Third party licensed        *
# proprietary data may also be incorporated herein and is subject to the       *
# terms of those licenses.                                                     *
# %OPLICENSE%                                                                  # 
################################################################################
LDIR = atst
include ../exec/Makefile.rules

LIBATGC = lib$(ACDES)atgc.a$(LINUXVERSION)

all : $(LIBATGC)

OBJS =              \
			at_ap.o    \
			at_ft.o    \
			atg_subs.o \
			atg_svn.o  \
			atgdiskd.o \
			atgpltrs.o \
			atgtitle.o \
			athist.o   \
			atrecord.o \
			dgnsubs.o  \
			xpidr.o    \
			xpidri.o   \
			xrlfi1.o

OBJ_SO = $(OBJS:%.o=%.so)

LIBATGC_OBJS = $(OBJS:%.o=$(LIBATGC)(%.o))

$(LIBATGC) : $(LIBATGC_OBJS)
	ln -fs $(LIBATGC) lib$(ACDES)atg.a

clean : 
	rm -f lib* *.o *.so .depend .depend_op

.INTERMEDIATE : $(OBJS) $(OBJ_SO)

# Automatic dependency rules
.depend_op: $(wildcard $(patsubst %,%/*.c,$(subst :, ,$(VPATH)))) makefile_op
	$(CC) $(CFLAGS) -MM -MG $(filter-out makefile_op, $^)  | \
	/usr/bin/gawk '{sub(/:/,": \044{FRC} \044{FRC_SO}");print}' >| .depend_op

include .depend_op
